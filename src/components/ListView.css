.list-view {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.empty-list {
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

.list-items {
  display: flex;
  flex-direction: column;
}

.list-item {
  border-bottom: 1px solid #f3f4f6;
  padding: 16px 20px;
  transition: all 0.3s ease;
  background: white;
}

.list-item:hover {
  background: #f9fafb;
}

.list-item:last-child {
  border-bottom: none;
}

/* Animation states */
.list-item.item-entering {
  animation: listItemEnter 0.4s ease;
}

.list-item.item-updating {
  animation: listItemUpdate 0.4s ease;
}

.list-item.item-exiting {
  animation: listItemExit 0.4s ease forwards;
}

@keyframes listItemEnter {
  from {
    opacity: 0;
    transform: translateX(-20px);
    background-color: #e6fffa;
  }
  to {
    opacity: 1;
    transform: translateX(0);
    background-color: white;
  }
}

@keyframes listItemUpdate {
  from {
    background-color: #fef5e7;
  }
  to {
    background-color: white;
  }
}

@keyframes listItemExit {
  to {
    opacity: 0;
    transform: translateX(-20px);
    max-height: 0;
    padding: 0 20px;
  }
}

/* List Item Header */
.list-item-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.list-item-number {
  width: 24px;
  height: 24px;
  background: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: #6b7280;
  flex-shrink: 0;
}

.list-item-primary {
  flex: 1;
  min-width: 0;
}

.primary-field-wrapper {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 32px;
  display: flex;
  align-items: center;
}

.primary-field-wrapper:hover {
  background: #f3f4f6;
}

.primary-field-wrapper:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

.list-item-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.list-item:hover .list-item-actions {
  opacity: 1;
}

.delete-record-btn {
  background: none;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
  color: #6b7280;
}

.delete-record-btn:hover {
  background: #fee2e2;
  color: #dc2626;
}

/* List Item Details */
.list-item-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-left: 36px; /* Align with primary field */
}

.detail-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-field-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.detail-field-value {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 28px;
  display: flex;
  align-items: center;
  border: 1px solid transparent;
}

.detail-field-value:hover {
  background: #f3f4f6;
  border-color: #e5e7eb;
}

.detail-field-value:focus {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .list-item {
    padding: 12px 16px;
  }
  
  .list-item-header {
    gap: 8px;
    margin-bottom: 8px;
  }
  
  .list-item-details {
    grid-template-columns: 1fr;
    gap: 8px;
    margin-left: 32px;
  }
  
  .list-item-actions {
    opacity: 1; /* Always show on mobile */
  }
  
  .detail-field {
    gap: 2px;
  }
  
  .detail-field-label {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .list-item-details {
    margin-left: 0;
    margin-top: 8px;
  }
  
  .list-item-header {
    flex-wrap: wrap;
  }
  
  .list-item-primary {
    order: 1;
    flex-basis: 100%;
    margin-top: 8px;
  }
}
