.airtable-view {
  max-width: 100%;
  margin: 0;
  padding: 0;
  background: #f8fafc;
  min-height: calc(100vh - 120px);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0;
  padding: 24px 24px 16px 24px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.table-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.table-header h2 {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
}

.table-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

.record-count {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 400;
}

.table-actions {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

.table-container {
  background: white;
  border-radius: 0;
  box-shadow: none;
  overflow: auto;
  max-height: calc(100vh - 300px);
  border-top: 1px solid #e2e8f0;
}

.airtable-grid {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 0.875rem;
}

.airtable-grid th,
.airtable-grid td {
  border-right: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  padding: 0;
  position: relative;
}

.airtable-grid th:last-child,
.airtable-grid td:last-child {
  border-right: none;
}

/* Header styles */
.row-number-header {
  width: 60px;
  background: #f7fafc;
  font-weight: 600;
  color: #4a5568;
  text-align: center;
  padding: 12px 8px;
  position: sticky;
  left: 0;
  z-index: 10;
}

.field-header {
  min-width: 150px;
  background: #f8fafc;
  padding: 0;
  position: relative;
  border-right: 1px solid #e2e8f0;
  transition: background-color 0.2s ease;
}

.field-header:hover {
  background: #f1f5f9;
}

.field-header-content {
  display: flex;
  flex-direction: column;
  padding: 8px 12px;
  min-height: 60px;
  position: relative;
}

.field-name {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
  cursor: pointer;
  transition: color 0.2s;
}

.field-name:hover {
  color: #4f46e5;
}

.field-name-btn {
  background: none;
  border: none;
  font-weight: 600;
  color: #2d3748;
  cursor: pointer;
  transition: color 0.2s;
  padding: 0;
  margin: 0;
  font-size: inherit;
  text-align: left;
}

.field-name-btn:hover {
  color: #4f46e5;
}

.field-type {
  font-size: 0.75rem;
  color: #718096;
  text-transform: capitalize;
}

.field-delete-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 20px;
  height: 20px;
  border: none;
  background: #e53e3e;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.field-header:hover .field-delete-btn {
  opacity: 1;
}



.field-options-count {
  font-size: 0.65rem;
  color: #9ca3af;
  margin-top: 2px;
}

.field-editor {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 12px;
}

.field-name-input {
  padding: 4px 8px;
  border: 1px solid #4f46e5;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 600;
}

.options-btn {
  padding: 4px 8px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.options-btn:hover {
  background: #e5e7eb;
}

.field-options-editor {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 100;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 12px;
}

.close-options-btn {
  width: 100%;
  padding: 8px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.2s;
}

.close-options-btn:hover {
  background: #4b5563;
}

.add-field-header {
  min-width: 120px;
  background: #f7fafc;
  padding: 8px;
}

.add-field-btn {
  width: 100%;
  padding: 8px;
  border: 2px dashed #cbd5e0;
  background: transparent;
  color: #4a5568;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.add-field-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
}

.add-field-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.add-field-form input,
.add-field-form select {
  padding: 4px 8px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 0.75rem;
}

.add-field-form button {
  padding: 4px 8px;
  font-size: 0.75rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
}

/* Row styles */
.table-row {
  transition: all 0.4s ease;
  border-bottom: 1px solid #f1f5f9;
}

.table-row:hover {
  background: linear-gradient(90deg, #f8fafc 0%, #f1f5f9 100%);
}

.row-entering {
  animation: rowEnter 0.4s ease;
}

.row-updating {
  animation: rowUpdate 0.4s ease;
}

.row-exiting {
  animation: rowExit 0.4s ease forwards;
}

@keyframes rowEnter {
  from {
    opacity: 0;
    transform: translateY(-10px);
    background-color: #e6fffa;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    background-color: transparent;
  }
}

@keyframes rowUpdate {
  from {
    background-color: #fef5e7;
  }
  to {
    background-color: transparent;
  }
}

@keyframes rowExit {
  to {
    opacity: 0;
    transform: translateX(-20px);
  }
}

.row-number {
  width: 60px;
  background: #f7fafc;
  text-align: center;
  padding: 12px 8px;
  position: sticky;
  left: 0;
  z-index: 5;
  font-weight: 500;
  color: #4a5568;
}

.row-delete-btn {
  position: absolute;
  top: 50%;
  right: 4px;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: none;
  background: #e53e3e;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s;
}

.row-number:hover .row-delete-btn {
  opacity: 1;
}

/* Cell styles */
.table-cell {
  min-width: 150px;
  padding: 0;
  height: 40px;
}

.cell-content {
  padding: 12px;
  min-height: 40px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.cell-content:hover {
  background-color: #f7fafc;
}

.cell-wrapper {
  width: 100%;
  height: 100%;
  min-height: 40px;
}

.cell-editor {
  padding: 4px;
}

.cell-editor input,
.cell-editor select {
  width: 100%;
  padding: 8px;
  border: 2px solid #4f46e5;
  border-radius: 4px;
  font-size: 0.875rem;
  outline: none;
}

.checkbox-display {
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-display input {
  cursor: pointer;
}

.empty-cell {
  min-width: 50px;
  background: #f7fafc;
}

/* Responsive design */
@media (max-width: 768px) {
  .airtable-view {
    padding: 10px;
  }
  
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .table-container {
    max-height: 60vh;
  }
  
  .airtable-grid th,
  .airtable-grid td {
    min-width: 120px;
  }
}
