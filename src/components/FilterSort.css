.filter-sort-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

.filter-sort-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.filter-sort-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.filter-btn,
.sort-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-btn:hover,
.sort-btn:hover {
  border-color: #4f46e5;
  background: #f8fafc;
}

.filter-btn.active,
.sort-btn.active {
  background: #4f46e5;
  color: white;
  border-color: #4f46e5;
}

.quick-sort-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.quick-sort-btn {
  padding: 4px 8px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
}

.quick-sort-btn:hover {
  border-color: #4f46e5;
  background: #f8fafc;
}

.quick-sort-btn.active {
  background: #e0e7ff;
  border-color: #4f46e5;
  color: #4338ca;
}

/* Filters Panel */
.filters-panel,
.sorts-panel {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
}

.filters-panel h4,
.sorts-panel h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.filter-item,
.sort-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 6px;
  margin-bottom: 8px;
  font-size: 0.875rem;
}

.filter-field,
.sort-field {
  font-weight: 500;
  color: #4f46e5;
}

.filter-operator {
  color: #6b7280;
}

.filter-value {
  color: #374151;
  font-style: italic;
}

.sort-index {
  font-weight: 600;
  color: #6b7280;
  min-width: 20px;
}

.sort-direction {
  color: #374151;
}

.remove-filter-btn,
.remove-sort-btn {
  margin-left: auto;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.remove-filter-btn:hover,
.remove-sort-btn:hover {
  background: #dc2626;
}

/* Add Filter Form */
.add-filter {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  margin-top: 12px;
}

.filter-field-select,
.filter-operator-select,
.filter-value-input {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  min-width: 120px;
}

.filter-value-input {
  min-width: 150px;
}

.filter-field-select:focus,
.filter-operator-select:focus,
.filter-value-input:focus {
  outline: none;
  border-color: #4f46e5;
}

.add-filter-btn {
  padding: 6px 12px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-filter-btn:hover:not(:disabled) {
  background: #059669;
}

.add-filter-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Sorts Panel */
.no-sorts {
  color: #6b7280;
  font-style: italic;
  margin: 0;
}

.sorts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filter-sort-header {
    padding: 8px 12px;
  }
  
  .quick-sort-buttons {
    gap: 4px;
  }
  
  .quick-sort-btn {
    font-size: 0.7rem;
    padding: 3px 6px;
  }
  
  .add-filter {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-field-select,
  .filter-operator-select,
  .filter-value-input {
    min-width: auto;
    width: 100%;
  }
  
  .filter-item,
  .sort-item {
    flex-wrap: wrap;
    gap: 4px;
  }
}
