/* Field Input Styles */
.field-input {
  width: 100%;
  padding: 8px 12px;
  border: 2px solid #4f46e5;
  border-radius: 6px;
  font-size: 0.875rem;
  outline: none;
  background: white;
  transition: border-color 0.2s;
}

.field-input:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Field Display Styles */
.field-display {
  padding: 8px 12px;
  min-height: 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.field-display:hover {
  background-color: #f8fafc;
}

/* Text Field */
.text-field {
  font-family: inherit;
}

/* Number Field */
.number-field {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-align: right;
}

/* Date Field */
.date-field {
  font-family: inherit;
}

/* Select Field */
.select-field {
  position: relative;
}

.select-field.has-value {
  padding: 4px 8px;
}

.select-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.select-placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* Checkbox Field */
.checkbox-field {
  justify-content: center;
  padding: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  background: white;
  transition: all 0.2s;
  position: relative;
}

.checkbox-input:checked + .checkbox-custom {
  background: #4f46e5;
  border-color: #4f46e5;
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-custom:hover {
  border-color: #4f46e5;
}

/* Field Type Selector */
.field-type-selector {
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.75rem;
  background: white;
  cursor: pointer;
}

.field-type-selector:focus {
  outline: none;
  border-color: #4f46e5;
}

/* Select Options Editor */
.select-options-editor {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.options-list {
  margin-bottom: 12px;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: #f9fafb;
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 0.875rem;
}

.remove-option-btn {
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.remove-option-btn:hover {
  background: #dc2626;
}

.add-option {
  display: flex;
  gap: 8px;
}

.add-option-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
}

.add-option-input:focus {
  outline: none;
  border-color: #4f46e5;
}

.add-option-btn {
  padding: 6px 12px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-option-btn:hover {
  background: #059669;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .field-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .field-display {
    padding: 12px 8px;
  }
  
  .select-options-editor {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 300px;
    z-index: 1000;
  }
}
