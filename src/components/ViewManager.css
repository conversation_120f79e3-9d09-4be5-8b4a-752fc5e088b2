.view-manager {
  position: relative;
  display: inline-block;
}

.current-view {
  position: relative;
}

.view-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.view-selector:hover {
  border-color: #4f46e5;
  background: #f8fafc;
}

.view-icon {
  font-size: 1rem;
  color: #6b7280;
}

.view-name {
  font-weight: 500;
  color: #374151;
}

.view-dropdown-arrow {
  font-size: 0.75rem;
  color: #9ca3af;
  transition: transform 0.2s;
}

.view-selector:hover .view-dropdown-arrow {
  transform: rotate(180deg);
}

/* View Menu */
.view-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  min-width: 300px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
}

.view-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.view-menu-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.close-menu-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-menu-btn:hover {
  background: #e5e7eb;
}

/* View List */
.view-list {
  max-height: 300px;
  overflow-y: auto;
}

.view-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.view-item:hover {
  background: #f9fafb;
}

.view-item.active {
  background: #eff6ff;
  border-left: 3px solid #4f46e5;
}

.view-info {
  flex: 1;
  cursor: pointer;
}

.view-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.view-stats {
  font-size: 0.75rem;
  color: #6b7280;
}

.active-indicator {
  color: #4f46e5;
  font-size: 0.75rem;
  margin-left: auto;
}

.view-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.view-item:hover .view-actions {
  opacity: 1;
}

.view-action-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.view-action-btn:hover {
  background: #e5e7eb;
}

.view-action-btn.delete:hover {
  background: #fee2e2;
}

/* View Edit */
.view-edit {
  flex: 1;
}

.view-name-input {
  width: 100%;
  padding: 6px 8px;
  border: 1px solid #4f46e5;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
}

.view-name-input:focus {
  outline: none;
  border-color: #6366f1;
}

/* View Menu Footer */
.view-menu-footer {
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 8px 8px;
}

.add-view-btn {
  width: 100%;
  padding: 8px 12px;
  background: #4f46e5;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-view-btn:hover {
  background: #4338ca;
}

/* Create View Form */
.create-view-form {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.new-view-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.new-view-input:focus {
  outline: none;
  border-color: #4f46e5;
}

.create-view-actions {
  display: flex;
  gap: 8px;
}

.create-view-btn {
  flex: 1;
  padding: 6px 12px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-view-btn:hover:not(:disabled) {
  background: #059669;
}

.create-view-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.cancel-view-btn {
  flex: 1;
  padding: 6px 12px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-view-btn:hover {
  background: #4b5563;
}

/* Responsive Design */
@media (max-width: 768px) {
  .view-menu {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    min-width: auto;
  }
  
  .view-actions {
    opacity: 1; /* Always show on mobile */
  }
}
